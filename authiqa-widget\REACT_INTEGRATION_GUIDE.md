# React SPA Integration Guide for Authiqa Widget

## Understanding the Redirection System

### 1. Navigation Between Auth Forms
The widget creates navigation links between different auth forms (signin ↔ signup). These use:
- **Custom paths** you provide (e.g., `signinAuthPath`, `signupAuthPath`)
- **Default API URLs** from the server if no custom paths provided
- **Hash navigation** for SPA compatibility

### 2. Post-Authentication Redirects
After successful authentication, the widget redirects using:
- `successAuthPath` - Where to go after successful login/signup
- `window.location.href` - Full page navigation

## Current Issues in Your Setup

1. **Missing `signupAuthPath`** - Your signin form can't link to signup
2. **Inconsistent configuration** - Some paths missing
3. **React routing conflict** - Widget tries full URL navigation instead of React routing

## Solution Options

### Option 1: Hash-Based Navigation (Recommended)

**Your corrected HTML configuration:**

```html
<script src="/global-config.js"></script>
<script
    src="https://www.staging.widget.authiqa.com"
    defer
    data-public-key="APK_9e5fb4ed3f72afcaf8031b7cf446d889_1751660923"
    action="signup"
    successAuthPath="/dashboard"
    resetAuthPath="#reset"
    signupAuthPath="#signup"
    signinAuthPath="#signin"
    verifyAuthPath="/confirm-email"
    resendAuthPath="#resend"
    termsAndConditions="/termsofservice"
    privacy="/privacypolicy"
    notificationSettings=""
    theme="dark"
></script>

<!-- Include hash navigation for SPA support -->
<script src="https://www.staging.widget.authiqa.com/hash-navigation.js" defer></script>
```

**Key Changes:**
- Added `signupAuthPath="#signup"`
- Changed `resetAuthPath` to `#reset` 
- Added `resendAuthPath="#resend"`
- Include hash-navigation.js script

### Option 2: React Router Integration

**In your React Router component:**

```tsx
// In your main App component or router setup
import { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';

function AuthWidget() {
    const navigate = useNavigate();
    const location = useLocation();

    useEffect(() => {
        // Listen for hash changes from the widget
        const handleHashChange = () => {
            const hash = window.location.hash.substring(1);
            if (['signin', 'signup', 'verify', 'reset', 'update', 'resend'].includes(hash)) {
                // Update React router to match
                navigate(`/auth/${hash}`, { replace: true });
            }
        };

        window.addEventListener('hashchange', handleHashChange);
        return () => window.removeEventListener('hashchange', handleHashChange);
    }, [navigate]);

    return <div id="authiqa"></div>;
}
```

### Option 3: Full React Router Paths

**If you want to use React routes instead of hashes:**

```html
<script
    src="https://www.staging.widget.authiqa.com"
    defer
    data-public-key="APK_9e5fb4ed3f72afcaf8031b7cf446d889_1751660923"
    action="signup"
    successAuthPath="/dashboard"
    resetAuthPath="/auth/reset"
    signupAuthPath="/auth/signup"
    signinAuthPath="/auth/signin"
    verifyAuthPath="/auth/verify"
    resendAuthPath="/auth/resend"
    termsAndConditions="/termsofservice"
    privacy="/privacypolicy"
    theme="dark"
></script>
```

**React Router Setup:**
```tsx
// In your router configuration
<Routes>
    <Route path="/auth/signin" element={<AuthWidget action="signin" />} />
    <Route path="/auth/signup" element={<AuthWidget action="signup" />} />
    <Route path="/auth/reset" element={<AuthWidget action="reset" />} />
    <Route path="/auth/verify" element={<AuthWidget action="verify" />} />
    <Route path="/auth/resend" element={<AuthWidget action="resend" />} />
    <Route path="/dashboard" element={<Dashboard />} />
</Routes>
```

## Path Types Explained

### Required for Navigation Between Forms:
- `signinAuthPath` - Link from signup/reset forms back to signin
- `signupAuthPath` - Link from signin form to signup
- `resetAuthPath` - Link from signin form to password reset

### Required for Post-Action Redirects:
- `successAuthPath` - After successful login/signup
- `verifyAuthPath` - After signup, where to verify email
- `resendAuthPath` - After signup, where to resend confirmation

### Path Format Options:
- **Hash-based**: `#signin`, `#signup` (best for SPAs)
- **Relative**: `/auth/signin`, `/auth/signup` 
- **Absolute**: `https://yoursite.com/auth/signin`

## Recommended Solution for Your Case

Use **Option 1 (Hash-based)** because:
1. ✅ Works seamlessly with React Router
2. ✅ No page reloads
3. ✅ Simple to implement
4. ✅ Widget handles all form switching
5. ✅ Your React app handles post-auth routing

## Next Steps

1. Update your HTML with the corrected configuration above
2. Include the hash-navigation.js script
3. Test the navigation between signin/signup forms
4. Verify post-authentication redirects work correctly
