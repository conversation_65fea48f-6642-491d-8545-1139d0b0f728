<!DOCTYPE html>
<html>
<head>
    <title>React SPA Integration - Authiqa Widget</title>
    <!-- Include the global config first -->
    <script src="./global-config.js"></script>
    
    <!-- Include the Authiqa widget -->
    <script
        src="https://www.staging.widget.authiqa.com"
        defer
        data-public-key="APK_9e5fb4ed3f72afcaf8031b7cf446d889_1751660923"
        action="signup"
        successAuthPath="/dashboard"
        resetAuthPath="/request-reset"
        signupAuthPath="#signup"
        signinAuthPath="#signin"
        verifyAuthPath="/confirm-email"
        resendAuthPath="#resend"
        termsAndConditions="/termsofservice"
        privacy="/privacypolicy"
        notificationSettings=""
        theme="dark"
    ></script>
    
    <!-- Include the hash navigation script for SPA support -->
    <script src="https://www.staging.widget.authiqa.com/hash-navigation.js" defer></script>
</head>
<body>
    <div id="authiqa"></div>
    
    <!-- React Router Integration Script -->
    <script>
        // Listen for hash changes and update React router accordingly
        window.addEventListener('hashchange', function() {
            const hash = window.location.hash.substring(1);
            if (['signin', 'signup', 'verify', 'reset', 'update', 'resend'].includes(hash)) {
                // Let the widget handle the form display
                // Your React router can also respond to these hash changes if needed
                console.log('Auth form changed to:', hash);
            }
        });
    </script>
</body>
</html>
